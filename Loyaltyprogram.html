<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loyalty Program</title>
    <style>
        /* Universal Color System - Change colors here to update everywhere */
        :root {
            --button-color: #A4F4FF;
            --primary-color: #17707F;
            --button-hover: #8EEEFF;
            --primary-hover: #0F5A66;
            --text-light: #FFFFFF;
            --text-dark: #333333;
            --background-light: #F8F9FA;
            --border-color: #E0E0E0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: var(--background-light);
            color: var(--text-dark);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .container {
            width: 100%;
            margin: 0;
            padding: 0;
        }

        /* Content wrapper with minimal padding only for readability */
        .content-wrapper {
            padding: 10px;
        }

        /* Override any Tailwind max-width classes */
        .max-w-7xl,
        .max-w-6xl,
        .max-w-5xl,
        .max-w-4xl,
        .max-w-3xl,
        .max-w-2xl,
        .max-w-xl,
        .max-w-lg,
        .max-w-md,
        .max-w-sm {
            max-width: 100% !important;
        }

        /* Full Width Layout Classes */
        .full-width-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .two-column-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .three-column-layout {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        /* Header */
        .header {
            background-color: var(--primary-color);
            color: var(--text-light);
            padding: 20px 10px;
            text-align: center;
            margin-bottom: 0;
            width: 100%;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* Navigation */
        .nav {
            background-color: var(--primary-color);
            padding: 15px 10px;
            margin-bottom: 0;
            width: 100%;
        }

        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            gap: 30px;
        }

        .nav a {
            color: var(--text-light);
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }

        .nav a:hover {
            background-color: var(--primary-hover);
        }

        /* Buttons */
        .btn {
            background-color: var(--button-color);
            color: var(--text-dark);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover {
            background-color: var(--button-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: var(--text-light);
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }

        .card h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        /* Points Display */
        .points-display {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: var(--text-light);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }

        .points-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        /* Rewards Grid */
        .rewards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .reward-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 2px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .reward-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-5px);
        }

        .reward-item img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 15px;
            border: 3px solid var(--button-color);
        }

        .reward-points {
            color: var(--primary-color);
            font-weight: bold;
            font-size: 1.2rem;
            margin-bottom: 15px;
        }

        /* Progress Bar */
        .progress-container {
            background-color: var(--border-color);
            border-radius: 10px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--button-color), var(--primary-color));
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        /* Footer */
        .footer {
            background-color: var(--primary-color);
            color: var(--text-light);
            text-align: center;
            padding: 30px 10px;
            margin-top: 20px;
            width: 100%;
        }

        /* Insights Card */
        .insights-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 2px solid var(--border-color);
        }

        .insights-card h3 {
            color: var(--text-dark);
            margin-bottom: 20px;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .insights-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .insights-row:last-child {
            border-bottom: none;
        }

        .insights-label {
            color: var(--text-dark);
            font-size: 1rem;
            font-weight: 500;
        }

        .insights-value {
            color: var(--primary-color);
            font-size: 1rem;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .three-column-layout {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 5px;
            }

            .nav ul {
                flex-direction: column;
                gap: 10px;
            }

            .rewards-grid {
                grid-template-columns: 1fr;
            }

            .two-column-layout {
                grid-template-columns: 1fr;
            }

            .three-column-layout {
                grid-template-columns: 1fr;
            }

            .points-number {
                font-size: 2rem;
            }

            .insights-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .insights-value {
                align-self: flex-end;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <h1>Loyalty Rewards Program</h1>
            <p>Earn points, unlock rewards, and enjoy exclusive benefits!</p>
        </div>
    </header>

    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="#dashboard">Dashboard</a></li>
                <li><a href="#rewards">Rewards</a></li>
                <li><a href="#history">History</a></li>
                <li><a href="#profile">Profile</a></li>
            </ul>
        </div>
    </nav>

    <main class="container">
        <div class="content-wrapper">
            <!-- Points Display -->
            <div class="points-display">
                <div class="points-number">2,450</div>
                <p>Your Current Points</p>
                <div class="progress-container">
                    <div class="progress-bar" style="width: 65%;"></div>
                </div>
                <p>650 points until next tier</p>
            </div>

            <!-- Two Column Layout for Insights and Quick Actions -->
            <div class="two-column-layout">
                <!-- Your 2025 Insights -->
                <div class="insights-card">
                    <h3>Your 2025 Insights</h3>
                    <div class="insights-row">
                        <span class="insights-label">Points Earned This Year:</span>
                        <span class="insights-value"></span>
                    </div>
                    <div class="insights-row">
                        <span class="insights-label">Discounts Redeemed:</span>
                        <span class="insights-value"></span>
                    </div>
                    <div class="insights-row">
                        <span class="insights-label">Most Frequent Category:</span>
                        <span class="insights-value"></span>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <h3>Quick Actions</h3>
                    <button class="btn">View Rewards</button>
                    <button class="btn btn-primary">Redeem Points</button>
                    <button class="btn">Refer Friends</button>
                    <button class="btn btn-primary">Check Balance</button>
                </div>
            </div>
        </div>

            <!-- Available Rewards -->
            <div class="card">
                <h3>Available Rewards</h3>
                <div class="rewards-grid">
                    <div class="reward-item">
                        <img src="https://via.placeholder.com/80" alt="Coffee">
                        <h4>Free Coffee</h4>
                        <div class="reward-points">500 Points</div>
                        <button class="btn">Redeem</button>
                    </div>
                    <div class="reward-item">
                        <img src="https://via.placeholder.com/80" alt="Discount">
                        <h4>20% Discount</h4>
                        <div class="reward-points">1,000 Points</div>
                        <button class="btn">Redeem</button>
                    </div>
                    <div class="reward-item">
                        <img src="https://via.placeholder.com/80" alt="Gift Card">
                        <h4>$25 Gift Card</h4>
                        <div class="reward-points">2,500 Points</div>
                        <button class="btn">Redeem</button>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card">
                <h3>Recent Activity</h3>
                <div style="margin-bottom: 15px;">
                    <strong>+50 points</strong> - Purchase at Store #123
                    <span style="float: right; color: var(--primary-color);">Today</span>
                </div>
                <div style="margin-bottom: 15px;">
                    <strong>+25 points</strong> - App Check-in
                    <span style="float: right; color: var(--primary-color);">Yesterday</span>
                </div>
                <div style="margin-bottom: 15px;">
                    <strong>-500 points</strong> - Redeemed Free Coffee
                    <span style="float: right; color: var(--primary-color);">2 days ago</span>
                </div>
                <button class="btn btn-primary">View Full History</button>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Loyalty Program. All rights reserved.</p>
            <p>Questions? Contact <NAME_EMAIL></p>
        </div>
    </footer>
</body>
</html>
